# Birthday Website - Easy Customization Guide

This beautiful birthday website can be easily customized for different people by simply changing the name in one file!

## How to Change the Birthday Person's Name

### Quick Method (Recommended):
1. Open the `config.js` file
2. Find this line: `name: "<PERSON><PERSON><PERSON><PERSON>",`
3. Change "<PERSON><PERSON><PERSON><PERSON>" to the new person's name (keep the quotes)
4. Save the file
5. Refresh the website - all instances of the name will be automatically updated!

### Example:
To change from "<PERSON><PERSON><PERSON><PERSON>" to "Neha":
```javascript
const BIRTHDAY_CONFIG = {
    name: "<PERSON><PERSON><PERSON>",  // Changed from "<PERSON><PERSON><PERSON><PERSON>" to "<PERSON>eh<PERSON>"
    // ... rest of the configuration stays the same
};
```

## What Gets Updated Automatically:
- Page title
- Welcome message
- Birthday card greeting
- All personal messages
- Cake decoration
- Final celebration text
- Neon celebration display
- Alert messages

## Advanced Customization:
You can also customize other messages in the `config.js` file:
- Welcome message
- Card messages
- Wish messages
- Final messages

Just edit the text in the `BIRTHDAY_CONFIG` object and use `{name}` as a placeholder where you want the person's name to appear.

## Files Structure:
- `index.html` - Main website file
- `styles.css` - All the beautiful styling
- `script.js` - Interactive functionality
- `config.js` - **THIS IS THE FILE YOU EDIT** to change names
- `README.md` - This instruction file

## Perfect for Selling:
This setup makes it incredibly easy to customize the website for different customers. Just change one line in `config.js` and you have a personalized birthday website for anyone!

---
*Enjoy creating magical birthday experiences! 🎉*
