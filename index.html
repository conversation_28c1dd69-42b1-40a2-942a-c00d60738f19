<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy Birthday Vaishnavi ❤️</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="config.js"></script>
</head>
<body>
    <!-- Floating Hearts Background -->
    <div class="hearts-container">
        <div class="heart"></div>
        <div class="heart"></div>
        <div class="heart"></div>
        <div class="heart"></div>
        <div class="heart"></div>
        <div class="heart"></div>
        <div class="heart"></div>
        <div class="heart"></div>
    </div>

    <!-- Background Flowers -->
    <div class="flowers-background">
        <div class="flower flower--1">
            <div class="flower__leafs flower__leafs--1">
                <div class="flower__leaf flower__leaf--1"></div>
                <div class="flower__leaf flower__leaf--2"></div>
                <div class="flower__leaf flower__leaf--3"></div>
                <div class="flower__leaf flower__leaf--4"></div>
                <div class="flower__white-circle"></div>
            </div>
            <div class="flower__line">
                <div class="flower__line__leaf flower__line__leaf--1"></div>
                <div class="flower__line__leaf flower__line__leaf--2"></div>
                <div class="flower__line__leaf flower__line__leaf--3"></div>
                <div class="flower__line__leaf flower__line__leaf--4"></div>
            </div>
        </div>
        <div class="flower flower--2">
            <div class="flower__leafs flower__leafs--2">
                <div class="flower__leaf flower__leaf--1"></div>
                <div class="flower__leaf flower__leaf--2"></div>
                <div class="flower__leaf flower__leaf--3"></div>
                <div class="flower__leaf flower__leaf--4"></div>
                <div class="flower__white-circle"></div>
            </div>
            <div class="flower__line">
                <div class="flower__line__leaf flower__line__leaf--1"></div>
                <div class="flower__line__leaf flower__line__leaf--2"></div>
                <div class="flower__line__leaf flower__line__leaf--3"></div>
                <div class="flower__line__leaf flower__line__leaf--4"></div>
            </div>
        </div>
        <div class="flower flower--3">
            <div class="flower__leafs flower__leafs--3">
                <div class="flower__leaf flower__leaf--1"></div>
                <div class="flower__leaf flower__leaf--2"></div>
                <div class="flower__leaf flower__leaf--3"></div>
                <div class="flower__leaf flower__leaf--4"></div>
                <div class="flower__white-circle"></div>
            </div>
            <div class="flower__line">
                <div class="flower__line__leaf flower__line__leaf--1"></div>
                <div class="flower__line__leaf flower__line__leaf--2"></div>
                <div class="flower__line__leaf flower__line__leaf--3"></div>
                <div class="flower__line__leaf flower__line__leaf--4"></div>
            </div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Welcome Section -->
        <section class="welcome-section" id="welcome">
            <div class="welcome-content">
                <h1 class="main-title">Happy Birthday</h1>
                <h2 class="sub-title">Beautiful Vaishnavi ❤️</h2>
                <p class="welcome-text">Today is your special day, Vaishnavi, and I want to make it magical for you!</p>
                <button class="start-btn" onclick="startJourney()">Start Your Birthday Journey</button>
            </div>
        </section>

        <!-- Birthday Card Section -->
        <section class="card-section hidden" id="cardSection">
            <div class="birthday-card" id="birthdayCard">
                <div class="card-front">
                    <div class="card-decoration">
                        <div class="balloon balloon-1">🎈</div>
                        <div class="balloon balloon-2">🎈</div>
                        <div class="balloon balloon-3">🎈</div>
                    </div>
                    <h3>Click to Open Your Special Card</h3>
                    <div class="gift-box">🎁</div>
                </div>
                <div class="card-inside">
                    <div class="card-left">
                        <h3>My Dearest Vaishnavi,</h3>
                        <p>On this beautiful day, I want you to know how much you mean to me. You are the sunshine in my life, the reason for my smile, and the beat of my heart.</p>
                        <p>Every moment with you is a treasure, Vaishnavi, and I'm grateful for every laugh, every hug, and every kiss we share.</p>
                        <p class="signature">Forever yours, ❤️</p>
                    </div>
                    <div class="card-right">
                        <div class="photo-frame">
                            <div class="photo-placeholder">
                                <span>Our Beautiful Memory</span>
                                <div class="heart-icon">💕</div>
                            </div>
                        </div>
                        <div class="wishes">
                            <p>🌟 May all your dreams come true, Vaishnavi</p>
                            <p>🎂 Wishing you endless happiness</p>
                            <p>💖 You deserve all the love in the world</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Special Messages Section -->
        <section class="messages-section hidden" id="messagesSection">
            <h2 class="section-title">Special Messages for You</h2>
            <div class="messages-container">
                <div class="message-card" data-message="1">
                    <div class="message-front">
                        <span class="message-number">1</span>
                        <p>Tap to reveal</p>
                    </div>
                    <div class="message-back">
                        <p>"You are my today and all of my tomorrows. Happy Birthday, Vaishnavi!"</p>
                    </div>
                </div>
                <div class="message-card" data-message="2">
                    <div class="message-front">
                        <span class="message-number">2</span>
                        <p>Tap to reveal</p>
                    </div>
                    <div class="message-back">
                        <p>"Every day with you feels like a celebration. Today, we celebrate YOU!"</p>
                    </div>
                </div>
                <div class="message-card" data-message="3">
                    <div class="message-front">
                        <span class="message-number">3</span>
                        <p>Tap to reveal</p>
                    </div>
                    <div class="message-back">
                        <p>"You make ordinary moments extraordinary. Happy Birthday, beautiful!"</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Birthday Cake Section -->
        <section class="cake-section hidden" id="cakeSection">
            <h2 class="section-title">Make a Wish! 🎂</h2>
            <div class="cake-container">
                <div id="cake-holder">
                    <div class="cake">
                        <div class="plate"></div>
                        <div class="layer layer-bottom"></div>
                        <div class="layer layer-middle"></div>
                        <div class="layer layer-top"></div>
                        <div class="icing"></div>
                        <div class="drip drip1"></div>
                        <div class="drip drip2"></div>
                        <div class="drip drip3"></div>
                        <div class="candle candle1">
                            <div class="flame" onclick="blowCandle(this, 1)"></div>
                        </div>
                        <div class="candle candle2">
                            <div class="flame" onclick="blowCandle(this, 2)"></div>
                        </div>
                        <div class="candle candle3">
                            <div class="flame" onclick="blowCandle(this, 3)"></div>
                        </div>
                    </div>
                    <div class="birthday-message">
                        <span class="birthday-text">Happy Birthday</span>
                        <span class="birthday-name">Vaishnavi</span>
                    </div>
                </div>
                <p class="cake-instruction">Click on the flames to blow out the candles!</p>
                <div class="wish-message hidden" id="wishMessage">
                    <h3>🌟 Your wish has been sent to the universe! 🌟</h3>
                    <p>May all your dreams come true, beautiful Vaishnavi!</p>
                </div>
            </div>
        </section>



        <!-- Final Message Section -->
        <section class="final-section hidden" id="finalSection">
            <div class="final-content">
                <h2>Happy Birthday, Vaishnavi! 💕</h2>
                <p>Thank you for being the most amazing person in my life, Vaishnavi. Here's to another year of love, laughter, and beautiful memories together!</p>
                <div class="celebration-btn" onclick="celebrate()">🎉 Celebrate! 🎉</div>
            </div>
        </section>
    </div>

    <!-- Confetti Container -->
    <div class="confetti-container" id="confettiContainer"></div>

    <!-- Full Screen Birthday Celebration Overlay -->
    <div class="birthday-celebration-overlay" id="birthdayCelebrationOverlay">
        <div class="celebration-background">
            <!-- Stars Background -->
            <div class="stars-container">
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
            </div>

            <!-- Fireworks Canvas -->
            <canvas id="fireworksCanvas"></canvas>

            <!-- Main Celebration Content -->
            <div class="celebration-content">
                <!-- Neon Happy Birthday Text -->
                <div class="neon-text-container">
                    <h1 class="neon-text">Happy Birthday</h1>
                    <h2 class="neon-name">Vaishnavi</h2>
                </div>




            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
