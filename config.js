// Birthday Website Configuration
// Change the name below to customize the website for different people

const BIRTHDAY_CONFIG = {
    // Main birthday person's name
    name: "<PERSON><PERSON><PERSON>",
    
    // Optional: You can also customize other text elements
    welcomeMessage: "Today is your special day, {name}, and I want to make it magical for you!",
    cardGreeting: "My Dearest {name},",
    cardMessage1: "On this beautiful day, I want you to know how much you mean to me. You are the sunshine in my life, the reason for my smile, and the beat of my heart.",
    cardMessage2: "Every moment with you is a treasure, {name}, and I'm grateful for every laugh, every hug, and every kiss we share.",
    wishMessage: "May all your dreams come true, {name}",
    loveMessage: "You are my today and all of my tomorrows. Happy Birthday, {name}!",
    finalWish: "May all your dreams come true, beautiful {name}!",
    finalMessage: "Thank you for being the most amazing person in my life, {name}. Here's to another year of love, laughter, and beautiful memories together!",

    // Photo captions
    photoCaptions: [
        "Our First Adventure Together 💕",
        "That Perfect Sunset Moment 🌅",
        "Laughing Until Our Stomachs Hurt 😂",
        "Dancing Like Nobody's Watching 💃",
        "Cozy Movie Night Together 🎬",
        "Making Memories Every Day ✨"
    ],

    photosMessage: "Every photo tells a story of our beautiful journey together. Here's to creating many more magical moments! 💖",

    // Alert message (shown in script.js)
    alertMessage: "🎉 Happy Birthday, {name}! Thank you for being the most wonderful person in my life! 💕"
};

// Function to replace placeholders with the actual name
function replacePlaceholders(text, name) {
    return text.replace(/{name}/g, name);
}

// Function to update all text elements on the page
function updateBirthdayContent() {
    const name = BIRTHDAY_CONFIG.name;
    
    // Update page title
    document.title = `Happy Birthday ${name} ❤️`;
    
    // Update main title
    const subTitle = document.querySelector('.sub-title');
    if (subTitle) {
        subTitle.textContent = `Beautiful ${name} ❤️`;
    }
    
    // Update welcome message
    const welcomeText = document.querySelector('.welcome-text');
    if (welcomeText) {
        welcomeText.textContent = replacePlaceholders(BIRTHDAY_CONFIG.welcomeMessage, name);
    }
    
    // Update card greeting
    const cardGreeting = document.querySelector('.card-left h3');
    if (cardGreeting) {
        cardGreeting.textContent = replacePlaceholders(BIRTHDAY_CONFIG.cardGreeting, name);
    }
    
    // Update card messages
    const cardMessages = document.querySelectorAll('.card-left p');
    if (cardMessages.length >= 2) {
        cardMessages[0].textContent = BIRTHDAY_CONFIG.cardMessage1;
        cardMessages[1].textContent = replacePlaceholders(BIRTHDAY_CONFIG.cardMessage2, name);
    }
    
    // Update wishes in card
    const wishElements = document.querySelectorAll('.wishes p');
    if (wishElements.length > 0) {
        wishElements[0].textContent = `🌟 ${replacePlaceholders(BIRTHDAY_CONFIG.wishMessage, name)}`;
    }
    
    // Update message cards
    const messageBack = document.querySelector('.message-back p');
    if (messageBack) {
        messageBack.textContent = `"${replacePlaceholders(BIRTHDAY_CONFIG.loveMessage, name)}"`;
    }
    
    // Update birthday name on cake
    const birthdayName = document.querySelector('.birthday-name');
    if (birthdayName) {
        birthdayName.textContent = name;
    }
    
    // Update wish message
    const wishMessageP = document.querySelector('#wishMessage p');
    if (wishMessageP) {
        wishMessageP.textContent = replacePlaceholders(BIRTHDAY_CONFIG.finalWish, name);
    }
    
    // Update final section
    const finalTitle = document.querySelector('.final-section h2');
    if (finalTitle) {
        finalTitle.textContent = `Happy Birthday, ${name}! 💕`;
    }
    
    const finalText = document.querySelector('.final-section p');
    if (finalText) {
        finalText.textContent = replacePlaceholders(BIRTHDAY_CONFIG.finalMessage, name);
    }
    
    // Update neon celebration text
    const neonName = document.querySelector('.neon-name');
    if (neonName) {
        neonName.textContent = name;
    }

    // Update photo captions
    const photoCaptions = document.querySelectorAll('.photo-caption');
    photoCaptions.forEach((caption, index) => {
        if (BIRTHDAY_CONFIG.photoCaptions[index]) {
            caption.textContent = BIRTHDAY_CONFIG.photoCaptions[index];
        }
    });

    // Update photos message
    const photosMessageP = document.querySelector('.photos-message p');
    if (photosMessageP) {
        photosMessageP.textContent = BIRTHDAY_CONFIG.photosMessage;
    }
}

// Function to update CSS content for pseudo-elements
function updateCSSContent() {
    const name = BIRTHDAY_CONFIG.name;

    // Create or update style element for dynamic CSS
    let styleElement = document.getElementById('dynamic-birthday-styles');
    if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'dynamic-birthday-styles';
        document.head.appendChild(styleElement);
    }

    // Update CSS with the new name
    styleElement.textContent = `
        .birthday-name::before {
            content: '${name}' !important;
        }

        .neon-name::before {
            content: '${name.toUpperCase()}' !important;
        }
    `;
}

// Auto-update content when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBirthdayContent();
    updateCSSContent();
});
